#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型训练管理器
整合模型训练的各个功能模块
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, Any, Optional, List
import threading
import time
import pandas as pd
import numpy as np

from ...core.base_gui import BaseGUI
from ...core.component_factory import get_component_factory
from ...core.event_manager import get_event_manager, EventTypes
from ...components.progress_widgets import ProgressWidget

# 导入算法模块
try:
    from ....algorithms import MODEL_TRAINERS, MODEL_NAMES, MODEL_DISPLAY_NAMES
    from ....algorithms.data_preprocessing import preprocess_data
    HAS_ALGORITHMS = True
except ImportError:
    MODEL_TRAINERS = {}
    MODEL_NAMES = []
    MODEL_DISPLAY_NAMES = {}
    HAS_ALGORITHMS = False


class ModelTrainingManager(BaseGUI):
    """模型训练管理器类"""

    def __init__(self, parent):
        """初始化模型训练管理器"""
        self.current_data = None
        self.preprocessed_data = None
        self.training_results = {}
        self.selected_models = []
        self.training_config = {
            'test_size': 0.2,
            'random_state': 42,
            'scaling_method': 'standard',
            'cross_validation': True,
            'cv_folds': 5
        }
        self.is_training = False

        super().__init__(parent)

        # 订阅相关事件
        self._bind_events()
    
    def _setup_ui(self):
        """设置UI界面"""
        factory = get_component_factory()

        # 主框架
        if self.parent:
            self.main_frame = factory.create_frame(self.parent)
            self.main_frame.pack(fill='both', expand=True, padx=5, pady=5)
        else:
            self.main_frame = None
            return

        # 创建主要内容区域
        self._create_main_content()
    
    def _create_main_content(self):
        """创建主要内容区域"""
        factory = get_component_factory()

        # 创建水平分割面板
        paned_window = ttk.PanedWindow(self.main_frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill='both', expand=True)

        # 左侧配置面板
        self._create_config_panel(paned_window)

        # 右侧结果面板
        self._create_results_panel(paned_window)

        self.register_component('paned_window', paned_window)
    
    def _create_config_panel(self, parent):
        """创建左侧配置面板"""
        factory = get_component_factory()

        # 配置面板框架
        config_frame = factory.create_frame(parent)
        config_frame.pack(fill='both', expand=True)

        # 创建滚动区域
        canvas = tk.Canvas(config_frame, width=350)
        scrollbar = ttk.Scrollbar(config_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = factory.create_frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 状态信息
        self._create_status_section(scrollable_frame)

        # 模型选择
        self._create_model_selection_section(scrollable_frame)

        # 训练配置
        self._create_training_config_section(scrollable_frame)

        # 控制按钮
        self._create_control_buttons_section(scrollable_frame)

        # 将配置面板添加到分割窗口
        parent.add(config_frame, weight=1)

        self.register_component('config_frame', config_frame)
    
    def _create_status_section(self, parent):
        """创建状态信息区域"""
        factory = get_component_factory()

        status_frame = factory.create_labelframe(parent, text="📊 训练状态")
        status_frame.pack(fill='x', padx=10, pady=5)

        self.status_label = factory.create_label(status_frame, text="等待数据加载...", style='info')
        self.status_label.pack(padx=10, pady=10)

        # 进度条
        self.progress_widget = ProgressWidget(status_frame, show_percentage=True)
        if self.progress_widget.main_frame:
            self.progress_widget.main_frame.pack(fill='x', padx=10, pady=(0, 10))

    def _create_model_selection_section(self, parent):
        """创建模型选择区域"""
        factory = get_component_factory()

        model_frame = factory.create_labelframe(parent, text="🤖 模型选择")
        model_frame.pack(fill='x', padx=10, pady=5)

        # 全选/取消全选按钮
        button_frame = factory.create_frame(model_frame)
        button_frame.pack(fill='x', padx=10, pady=5)

        select_all_btn = factory.create_button(button_frame, text="全选",
                                             command=self._select_all_models, style='small')
        select_all_btn.pack(side='left', padx=(0, 5))

        deselect_all_btn = factory.create_button(button_frame, text="取消全选",
                                                command=self._deselect_all_models, style='small')
        deselect_all_btn.pack(side='left')

        # 模型复选框
        self.model_vars = {}
        models_info = {
            'DecisionTree': '决策树 - 快速、可解释',
            'RandomForest': '随机森林 - 高精度、抗过拟合',
            'XGBoost': 'XGBoost - 梯度提升、高性能',
            'LightGBM': 'LightGBM - 快速梯度提升',
            'CatBoost': 'CatBoost - 处理类别特征',
            'LogisticRegression': '逻辑回归 - 简单、快速',
            'SVM': '支持向量机 - 适合小数据集',
            'KNN': 'K近邻 - 非参数方法',
            'NaiveBayes': '朴素贝叶斯 - 适合文本分类',
            'NeuralNet': '神经网络 - 复杂模式识别'
        }

        for model_name, description in models_info.items():
            var = tk.BooleanVar()
            self.model_vars[model_name] = var

            cb = factory.create_checkbutton(model_frame, text=description, variable=var)
            cb.pack(anchor='w', padx=20, pady=2)
    
    def _create_tuning_content(self, parent):
        """创建超参数调优内容"""
        factory = get_component_factory()
        
        container = factory.create_frame(parent)
        container.pack(fill='both', expand=True, padx=20, pady=20)
        
        title_label = factory.create_label(container, text="超参数调优", style='title')
        title_label.pack(pady=(0, 20))
        
        info_text = "超参数调优功能包括：\n\n• 网格搜索 (Grid Search)\n• 随机搜索 (Random Search)\n• 贝叶斯优化\n• 交叉验证\n• 参数重要性分析\n\n该功能将帮助您找到最优的模型参数组合。"
        
        info_label = factory.create_label(container, text=info_text, style='secondary')
        info_label.pack(anchor='w')
    
    def _bind_events(self):
        """绑定事件"""
        event_manager = get_event_manager()
        event_manager.subscribe(EventTypes.DATA_LOADED, self._on_data_loaded)
        event_manager.subscribe(EventTypes.DATA_PREPROCESSED, self._on_data_preprocessed)
    
    def _start_training(self):
        """开始训练"""
        if not self.current_data:
            self.show_warning("警告", "请先加载数据！")
            return
        
        # 更新状态
        self.status_label.config(text="正在训练模型...")
        self.train_button.config(state='disabled')
        
        # 模拟训练过程
        self._simulate_training()
    
    def _simulate_training(self):
        """模拟训练过程"""
        import threading
        import time
        
        def train():
            try:
                # 模拟训练时间
                time.sleep(2)
                
                # 模拟训练结果
                results = {
                    'DecisionTree': {'accuracy': 0.85, 'f1_score': 0.83},
                    'RandomForest': {'accuracy': 0.89, 'f1_score': 0.87},
                    'LogisticRegression': {'accuracy': 0.82, 'f1_score': 0.80},
                    'SVM': {'accuracy': 0.86, 'f1_score': 0.84}
                }
                
                # 在主线程中更新UI
                if self.main_frame:
                    self.main_frame.after(0, lambda: self._training_completed(results))
                
            except Exception as e:
                if self.main_frame:
                    self.main_frame.after(0, lambda: self._training_failed(str(e)))
        
        # 在后台线程中运行训练
        training_thread = threading.Thread(target=train)
        training_thread.daemon = True
        training_thread.start()
    
    def _training_completed(self, results):
        """训练完成"""
        self.training_results = results
        
        # 更新状态
        self.status_label.config(text=f"训练完成！成功训练 {len(results)} 个模型")
        self.train_button.config(state='normal')
        
        # 显示结果
        self.results_text.config(state='normal')
        self.results_text.delete('1.0', 'end')
        
        result_text = "训练结果：\n\n"
        for model, metrics in results.items():
            result_text += f"{model}:\n"
            result_text += f"  准确率: {metrics['accuracy']:.3f}\n"
            result_text += f"  F1分数: {metrics['f1_score']:.3f}\n\n"
        
        self.results_text.insert('1.0', result_text)
        self.results_text.config(state='disabled')
        
        # 发布训练完成事件
        event_manager = get_event_manager()
        event_manager.publish(EventTypes.MODEL_TRAINED, {
            'results': self.training_results
        })
    
    def _training_failed(self, error_message):
        """训练失败"""
        self.status_label.config(text=f"训练失败: {error_message}")
        self.train_button.config(state='normal')
        self.show_error("训练失败", f"模型训练过程中出现错误:\n{error_message}")
    
    def _on_data_loaded(self, event_data):
        """数据加载事件处理"""
        try:
            if event_data and 'data' in event_data:
                self.current_data = event_data['data']
                self.status_label.config(text=f"✅ 数据已加载: {self.current_data.shape[0]} 行, {self.current_data.shape[1]} 列")
                self.train_button.config(state='normal')
                self.logger.info(f"模型训练模块已加载数据: {self.current_data.shape}")
            else:
                self.logger.warning("数据加载事件中缺少'data'字段")
                self.status_label.config(text="❌ 数据加载失败")
        except Exception as e:
            self.logger.error(f"处理数据加载事件时出错: {e}")
            self.status_label.config(text="❌ 数据加载失败")
    
    def _on_data_preprocessed(self, event_data):
        """数据预处理事件处理"""
        try:
            if event_data and all(key in event_data for key in ['X_train', 'X_test', 'y_train', 'y_test']):
                # 更新状态显示训练和测试数据已准备好
                train_size = event_data['X_train'].shape[0]
                test_size = event_data['X_test'].shape[0]
                feature_count = event_data['X_train'].shape[1]
                
                self.status_label.config(
                    text=f"✅ 数据已预处理 (训练集: {train_size} 行, 测试集: {test_size} 行, 特征: {feature_count} 个)"
                )
                self.train_button.config(state='normal')
                self.logger.info(f"模型训练模块已接收预处理数据: 训练集{train_size}行, 测试集{test_size}行")
            elif event_data and 'data' in event_data:
                # 如果只有原始数据，显示预处理完成但未分割
                data = event_data['data']
                self.status_label.config(text=f"✅ 数据已预处理 ({data.shape[0]} 行, {data.shape[1]} 列) - 请手动进行数据分割")
                self.logger.info(f"模型训练模块已接收预处理数据: {data.shape}")
            else:
                self.logger.warning("数据预处理事件中缺少必要字段")
                self.status_label.config(text="❌ 数据预处理失败")
        except Exception as e:
            self.logger.error(f"处理数据预处理事件时出错: {e}")
            self.status_label.config(text="❌ 数据预处理失败")
    
    def get_current_data(self) -> Optional[Dict[str, Any]]:
        """获取当前数据"""
        return {'dataframe': self.current_data} if self.current_data is not None else None
    
    def get_training_results(self) -> Dict[str, Any]:
        """获取训练结果"""
        return self.training_results.copy()
    
    def clear_results(self):
        """清空所有结果"""
        self.training_results = {}
        if hasattr(self, 'results_text'):
            self.results_text.config(state='normal')
            self.results_text.delete('1.0', 'end')
            self.results_text.insert('1.0', "训练结果将在这里显示...")
            self.results_text.config(state='disabled')
